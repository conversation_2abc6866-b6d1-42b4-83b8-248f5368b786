// SimpleInfiniteTable.tsx
import {
  Table as CTable,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Box,
  Text,
  Spinner,
} from '@chakra-ui/react';
import { useSuspenseInfiniteQuery } from '@tanstack/react-query';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  type ColumnDef,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useEffect, useMemo, useRef } from 'react';

/** Tipo do shape da resposta paginada do backend */
export type PageResult<T> = { items: T[]; nextCursor?: unknown };

/** Props mínimas do nosso exemplo */
type SimpleInfiniteTableProps<TData> = {
  /** Colunas TanStack Table */
  columns: ColumnDef<TData, any>[];
  /** Altura do container (px) */
  height?: number;
  /** Estimativa da altura de cada linha (px) */
  estimateSize?: number;
  /** Chave de cache do React Query */
  queryKey: (string | number)[];
  /**
   * Função que busca UMA página (cursor-based ou page-based).
   * Recebe pageParam e deve retornar { items, nextCursor }.
   */
  queryFn: (ctx: { pageParam?: unknown }) => Promise<PageResult<TData>>;
  /**
   * Extrai o cursor da última página. Ex.: (last) => last.nextCursor ?? undefined
   */
  getNextPageParam: (lastPage: PageResult<TData>) => unknown | undefined;
  /** (Opcional) getRowId estável quando o objeto não tem "id" */
  getRowId?: (original: TData, index: number) => string;
  /** (Opcional) função para obter cor de fundo da linha */
  getRowBackgroundColor?: (original: TData, index: number) => string;
};

export function SimpleInfiniteTable<TData>({
  columns,
  height = 480,
  estimateSize = 44,
  queryKey,
  queryFn,
  getNextPageParam,
  getRowId,
  getRowBackgroundColor,
}: SimpleInfiniteTableProps<TData>) {
  // 1) Buscar páginas com Suspense + InfiniteQuery
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useSuspenseInfiniteQuery({
      queryKey,
      queryFn,
      getNextPageParam,
      initialData: {
        pages: [] as PageResult<TData>[],
        pageParams: [] as unknown[],
      },
      initialPageParam: 0,
    });

  // 2) “Achatamos” as páginas num único array
  const flatData = useMemo(() => data.pages.flatMap((p) => p.items), [data]);

  // 3) Criamos a instância do TanStack Table
  const table = useReactTable({
    data: flatData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getRowId, // opcional mas recomendado se seu objeto não tem "id"
  });

  // 4) Virtualização de linhas (+1 linha “loader” no final)
  const parentRef = useRef<HTMLDivElement | null>(null);
  const rowCount = table.getRowModel().rows.length + (hasNextPage ? 1 : 0);

  const rowVirtualizer = useVirtualizer({
    count: rowCount,
    getScrollElement: () => parentRef.current,
    estimateSize: () => estimateSize,
    overscan: 8,
  });

  // 5) Quando a última linha virtual for a “loader row”, buscar mais
  useEffect(() => {
    const items = rowVirtualizer.getVirtualItems();
    if (!items.length) return;
    const last = items[items.length - 1];
    const isLoaderRow = last.index >= table.getRowModel().rows.length;
    if (isLoaderRow && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage, rowVirtualizer, table]);

  // 6) Render
  return (
    <Box borderWidth="1px" rounded="md" overflow="hidden">
      {/* Header “normal” */}
      <CTable variant="striped" size="sm">
        <Thead>
          {table.getHeaderGroups().map((hg) => (
            <Tr key={hg.id}>
              {hg.headers.map((h) => (
                <Th key={h.id}>
                  {h.isPlaceholder
                    ? null
                    : flexRender(h.column.columnDef.header, h.getContext())}
                </Th>
              ))}
            </Tr>
          ))}
        </Thead>
      </CTable>

      {/* Corpo virtualizado */}
      <Box
        ref={parentRef}
        maxH={`${height}px`}
        overflowY="auto"
        position="relative"
      >
        {/* Canvas com altura total calculada pelo virtualizer */}
        <Box height={rowVirtualizer.getTotalSize()} position="relative">
          {rowVirtualizer.getVirtualItems().map((vi) => {
            const rows = table.getRowModel().rows;
            const isLoaderRow = vi.index >= rows.length;
            const row = rows[vi.index];

            return (
              <Box
                key={vi.key}
                position="absolute"
                top={0}
                left={0}
                right={0}
                transform={`translateY(${vi.start}px)`}
              >
                {/* Uma “mini tabela” por item mantém semântica simples */}
                <CTable size="sm" variant="unstyled">
                  <Tbody>
                    <Tr
                      bg={
                        !isLoaderRow && getRowBackgroundColor
                          ? getRowBackgroundColor(row.original, vi.index)
                          : undefined
                      }
                    >
                      {isLoaderRow ? (
                        <Td colSpan={table.getAllLeafColumns().length}>
                          <Box py={2} textAlign="center">
                            {isFetchingNextPage ? (
                              <Spinner size="xs" />
                            ) : hasNextPage ? (
                              <Text fontSize="sm" color="gray.500">
                                Role para carregar mais…
                              </Text>
                            ) : (
                              <Text fontSize="sm" color="gray.500">
                                Fim 🎉
                              </Text>
                            )}
                          </Box>
                        </Td>
                      ) : (
                        row.getVisibleCells().map((cell) => (
                          <Td
                            key={cell.id}
                            bg={
                              getRowBackgroundColor
                                ? getRowBackgroundColor(row.original, vi.index)
                                : undefined
                            }
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </Td>
                        ))
                      )}
                    </Tr>
                  </Tbody>
                </CTable>
              </Box>
            );
          })}
        </Box>
      </Box>
    </Box>
  );
}
