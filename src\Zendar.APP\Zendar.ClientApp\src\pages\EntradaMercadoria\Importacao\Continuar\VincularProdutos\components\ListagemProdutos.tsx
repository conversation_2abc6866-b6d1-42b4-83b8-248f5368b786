import {
  Box,
  useMediaQuery,
  Text,
  Icon,
  HStack,
  Flex,
  Button,
} from '@chakra-ui/react';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo, useRef } from 'react';
import { FiCheckCircle, FiChevronUp } from 'react-icons/fi';

import { DecimalMask } from 'helpers/format/fieldsMasks';
import useWindowSize from 'helpers/layout/useWindowSize';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import { SimpleInfiniteTable, PageResult } from 'components/update/Table/Test';
import { LoadMoreRowsParams } from 'components/update/Table/VirtualizedInfiniteTable';

import {
  Produto,
  InformacoesRodape,
  EntradaMercadoriaStatusVinculoProduto,
} from '../hooks/useProdutosVinculacao';
import { TextoTooltip } from '../TextoTooltip';

interface ListagemProdutosProps {
  produtos: Produto[];
  informacoesRodape: InformacoesRodape;
  isLoading: boolean;
  handleToggleLinhaProduto: (index: number) => void;
  handleEditar: (index: number) => Promise<void>;
  handleVincularProduto: (
    index: number,
    produtoPendenteVariacoes?: ProdutoOptionProps
  ) => Promise<void>;
  loadMoreRows: (params: LoadMoreRowsParams) => Promise<void>;
  modoTelaCheia?: boolean;
}

export function ListagemProdutos({
  produtos,
  informacoesRodape,
  isLoading,
  handleToggleLinhaProduto,
  handleEditar,
  handleVincularProduto,
  loadMoreRows,
  modoTelaCheia = false,
}: ListagemProdutosProps) {
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const { height: windowHeight } = useWindowSize();

  const { casasDecimais } = usePadronizacaoContext();

  const obterCorBackground = (produto: Produto): string => {
    const enumStatus = EntradaMercadoriaStatusVinculoProduto;
    const vinculado = produto.statusVinculo === enumStatus.VINCULADO;
    const naoVinculado = produto.statusVinculo === enumStatus.NAO_VINCULADO;

    if (vinculado) return 'teal.600';
    if (naoVinculado) return 'white';
    return 'aquamarine.100';
  };

  const colunas: ColumnDef<Produto, any>[] = useMemo(
    () => [
      {
        id: 'descricaoProduto',
        header: 'Produto',
        cell: ({ row }) => {
          const produto = row.original;
          const index = row.index;

          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          const statusQuePodemMostrarDetalhes = [
            EntradaMercadoriaStatusVinculoProduto.VINCULADO,
            EntradaMercadoriaStatusVinculoProduto.PENDENTE_INFORMAR_VARIACOES,
          ];
          const podeMostrarDetalhes =
            statusQuePodemMostrarDetalhes.includes(produto.statusVinculo) ||
            !!produto.dadosAdicionais;

          return (
            <Box
              cursor={podeMostrarDetalhes ? 'pointer' : 'default'}
              userSelect="none"
              fontSize="14px"
              onClick={() => {
                if (podeMostrarDetalhes) handleToggleLinhaProduto(index);
              }}
              color={produtoEstaVinculado ? 'white' : 'inherit'}
            >
              <Button
                tabIndex={0}
                bg="transparent"
                p="4px"
                pb="0px"
                mr="6px"
                h="fit-content"
                borderRadius="6px"
                _focus={{ background: 'gray.100' }}
                minW="16px"
                opacity={podeMostrarDetalhes ? '1' : '0'}
                pointerEvents={podeMostrarDetalhes ? 'all' : 'none'}
              >
                <Icon
                  as={FiChevronUp}
                  mb="6px"
                  transform={produto.isOpen ? '' : 'rotate(180deg)'}
                  role="button"
                  transition="all 0.3s"
                />
              </Button>
              {produto.descricaoProdutoNota}
              {produto.isOpen && produto.dadosAdicionais && (
                <Flex
                  position="absolute"
                  w="97%"
                  flexDir="row"
                  align="center"
                  pl="30px"
                  mt="4px"
                  gap="4px"
                  fontSize="12px"
                  fontWeight="bold"
                >
                  <TextoTooltip
                    texto={produto.dadosAdicionais}
                    maxWidth="100%"
                  />
                </Flex>
              )}
            </Box>
          );
        },
      },
      {
        id: 'quantidade',
        header: 'Quantidade',
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          return (
            <Text
              fontSize="14px"
              color={produtoEstaVinculado ? 'white' : 'inherit'}
            >
              {DecimalMask(
                produto.quantidade,
                casasDecimais.casasDecimaisQuantidade
              )}
            </Text>
          );
        },
      },
      {
        id: 'valorUnitario',
        header: 'Valor unitário',
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          return (
            <Text
              fontSize="14px"
              textAlign="right"
              color={produtoEstaVinculado ? 'white' : 'inherit'}
            >
              {DecimalMask(
                produto.valorUnitario,
                casasDecimais.casasDecimaisValor
              )}
            </Text>
          );
        },
      },
      {
        id: 'valorTotal',
        header: 'Valor total',
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          return (
            <Text
              fontSize="14px"
              textAlign="right"
              color={produtoEstaVinculado ? 'white' : 'inherit'}
            >
              {DecimalMask(produto.valorTotal, 2, 2)}
            </Text>
          );
        },
      },
      {
        id: 'acoes',
        header: 'Ações',
        cell: ({ row }) => {
          const produto = row.original;
          const index = row.index;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          const produtoNaoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.NAO_VINCULADO;

          const handleVincularClick = () => {
            if (produtoNaoEstaVinculado) {
              handleVincularProduto(index);
              return;
            }

            handleVincularProduto(index, {
              id: produto.produtoVinculado?.id || '',
              nome: produto.produtoVinculado?.nome || '',
              tipoProduto: produto.produtoVinculado?.tipoProduto || 2,
              volumeUnitario: produto.produtoVinculado?.volumeUnitario || false,
              referencia: produto.produtoVinculado?.referencia || '',
              precoCompra: produto.produtoVinculado?.precoCompra || 0,
              coresOptions: [],
              tamanhosOptions: [],
            });
          };

          if (produtoEstaVinculado) {
            return (
              <Flex justifyContent="space-between">
                <HStack spacing="1" color="secondary.300">
                  <Icon as={FiCheckCircle} boxSize="4" />
                  <Text fontSize="xs">Vinculado</Text>
                </HStack>
                <ActionsMenu
                  colorScheme="white"
                  backgroundHoverColor="gray.500"
                  menuZIndex="popover"
                  items={[
                    {
                      content: 'Editar',
                      onClick: () => handleEditar(index),
                    },
                  ]}
                />
              </Flex>
            );
          }

          return (
            <Flex alignItems="center" justifyContent="center">
              <Button
                size="xs"
                colorScheme="orange"
                minW="136px"
                onClick={handleVincularClick}
              >
                {produtoNaoEstaVinculado
                  ? 'Vincular ao sistema'
                  : 'Informar variações'}
              </Button>
            </Flex>
          );
        },
      },
    ],
    [
      handleToggleLinhaProduto,
      handleEditar,
      handleVincularProduto,
      casasDecimais,
    ]
  );

  const getFixedTableHeight = (): number => {
    const maxVisibleItems = 8;
    const averageItemHeight = 64;
    return maxVisibleItems * averageItemHeight;
  };

  return (
    // <Box
    //   display="flex"
    //   flexDirection="column"
    //   justifyContent="space-between"
    //   borderRadius="md"
    //   border="1px"
    //   bg="gray.50"
    //   borderColor="gray.200"
    //   maxH={modoTelaCheia ? `${alturaDisponivelModal}px` : maxContainerHeight}
    //   py={{ base: 4, sm: 6, md: 6 }}
    //   pl={{ base: 4, sm: 6, md: 6 }}
    //   pb={modoTelaCheia ? '60px' : 0}
    //   pr={{ base: '6px', sm: '14px', md: '24px' }}
    //   sx={{
    //     '& table': { bg: 'gray.50' },
    //     '& thead > tr > th': {
    //       bg: 'gray.50',
    //       border: 'none',
    //     },
    //     '& td:first-of-type': {
    //       paddingLeft: '16px !important',
    //     },
    //     '& tbody > tr': {
    //       borderRadius: 'md',
    //       boxShadow: '0px 0px 2px #00000029',
    //       ...(informacoesRodape.totalProdutos > 0
    //         ? { border: '1px', borderColor: 'gray.100' }
    //         : {
    //             '& > td': {
    //               position: 'relative',
    //               _before: {
    //                 content: '""',
    //                 position: 'absolute',
    //                 h: 'full',
    //                 w: 'full',
    //                 top: 0,
    //                 left: 0,
    //                 borderLeft: 'none',
    //                 borderRight: 'none',
    //                 borderRadius: 'md',
    //               },
    //             },
    //           }),
    //     },
    //     '& tbody > tr > td': {
    //       bg: 'white',
    //       lineHeight: 'none',
    //       _before: {
    //         border:
    //           informacoesRodape.totalProdutos > 0 ? 'none !important' : '1px',
    //         borderColor: 'gray.100',
    //       },
    //     },
    //   }}
    // >
    //   <Box
    //     sx={{
    //       '& table': { bg: 'gray.50' },
    //       '& thead > tr > th': {
    //         bg: 'gray.50',
    //         border: 'none',
    //       },
    //       '& tbody > tr': {
    //         borderRadius: 'md',
    //         boxShadow: '0px 0px 2px #00000029',
    //         ...(informacoesRodape.totalProdutos > 0
    //           ? { border: '1px', borderColor: 'gray.100' }
    //           : {
    //               '& > td': {
    //                 position: 'relative',
    //                 _before: {
    //                   content: '""',
    //                   position: 'absolute',
    //                   h: 'full',
    //                   w: 'full',
    //                   top: 0,
    //                   left: 0,
    //                   borderLeft: 'none',
    //                   borderRight: 'none',
    //                   borderRadius: 'md',
    //                 },
    //               },
    //             }),
    //       },
    //       '& tbody > tr > td': {
    //         bg: 'white',
    //         lineHeight: 'none',
    //         _before: {
    //           border:
    //             informacoesRodape.totalProdutos > 0 ? 'none !important' : '1px',
    //           borderColor: 'gray.100',
    //         },
    //       },
    //     }}
    //   >

    //   </Box>
    // </Box>

  );
}
